/**
 * mainEntry.js - 主进程入口文件
 *
 * 该文件是Electron应用程序的主进程入口点，负责创建和管理应用程序窗口、
 * 处理应用程序生命周期事件、设置IPC通信、配置应用程序行为等核心功能。
 *
 * 主要功能包括：
 * 1. 应用程序窗口的创建与管理（主窗口和加载窗口）
 * 2. 应用程序单实例锁定，防止多开
 * 3. 应用程序更新检测与通知
 * 4. 全局快捷键注册
 * 5. 自定义协议注册（用于生产环境资源加载）
 * 6. 主进程与渲染进程间的IPC通信
 */

import {
  app,                // 控制应用程序的事件生命周期，包括启动、退出和系统事件处理
  BrowserWindow,      // 创建和控制浏览器窗口，提供窗口操作和状态管理功能
  crashReporter,      // 崩溃报告功能，用于收集和处理应用崩溃信息
  shell,              // 提供与桌面集成的功能，如打开外部URL或文件
  Tray,               // 系统托盘功能，用于在系统托盘区域显示图标和菜单
  nativeImage,        // 处理本地图像，用于创建托盘图标等原生UI元素
  ipcMain,            // 主进程IPC通信，用于主进程和渲染进程之间的消息传递
  globalShortcut,     // 全局快捷键，用于注册和响应系统级键盘快捷键
  session
} from "electron";
import path from "path";  // Node.js路径模块，用于处理文件路径

// 导入自定义协议处理模块，用于在生产环境中加载本地资源
import { CustomScheme } from "./customScheme.js";

// 导入窗口事件处理模块，用于注册和处理窗口事件
import { CommonWindowEvent } from "./commonWindowEvent.js";
// 导入更新模块，用于检查和处理应用程序更新
import { Updater } from "./updater.js";
// 导入操作系统模块，用于获取系统信息如MAC地址
import os from "os";

// 禁用Electron安全警告，通常在开发环境中使用
// 这可以避免控制台中显示大量与安全相关的警告信息，使开发过程更加清晰
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = "true";

//崩溃报告 当应用程序崩溃时，就会产生一个.dmp 扩展名结尾的文件（存放于 C:\Users\<USER>\AppData\Roaming\[yourAppName]\Crashpad
// 初始化崩溃报告器，但不上传到服务器
// 这样可以在本地保存崩溃报告以便调试，但不会将用户数据发送到外部服务器
crashReporter.start({ submitURL: "", uploadToServer: false });

// 禁用硬件加速，可以解决某些显卡兼容性问题
// 在某些系统上，硬件加速可能导致渲染问题或性能下降，禁用它可以提高稳定性
app.disableHardwareAcceleration();

// 忽略HTTPS证书错误，用于开发环境或特定场景
// 这允许应用加载自签名证书或证书过期的HTTPS资源，主要用于开发测试
app.commandLine.appendSwitch("ignore-certificate-errors");

// 每当有一个窗口被创建成功后，这个事件就会被触发
app.on("browser-window-created", (e, win) => {
  // 为每个新创建的窗口注册通用窗口事件处理
  CommonWindowEvent.regWinEvent(win);
});

// 定义全局变量
var timer;      // 用于存储更新检查的定时器ID
let mainWindow; // 主应用窗口实例
let loading;    // 加载窗口实例

// 限制应用多开 - 确保应用程序只有一个实例在运行
// 这是一种单例模式的实现，防止用户同时运行多个应用实例导致的资源冲突和数据不一致
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  // 如果无法获取锁（即已有实例在运行），则退出当前实例
  // 这样可以确保系统中只有一个应用实例在运行，避免资源浪费和潜在的数据冲突
  app.quit();
} else {
  // 如果获取到锁（当前是第一个实例），则监听第二个实例的启动
  // 当用户尝试启动第二个实例时，这个事件处理器会被触发
  app.on("second-instance", (event, commandLine, workingDirectory) => {
    // 当运行第二个实例时,将会聚焦到mainWindow这个窗口
    // 这提供了更好的用户体验，而不是简单地忽略第二次启动尝试
    if (mainWindow) {
      // 如果主窗口被最小化，则恢复窗口
      // 确保窗口可见，不管它之前处于什么状态
      if (mainWindow.isMinimized()) mainWindow.restore();
      // 聚焦并显示主窗口，确保用户能看到应用
      // 这两个操作确保窗口不仅可见，而且处于活动状态并在前台显示
      mainWindow.focus();
      mainWindow.show();
    }
  });
}
/**
 * 创建主应用窗口
 *
 * 该函数负责创建和配置应用程序的主窗口，设置窗口属性、事件监听和内容加载
 */
function createWindow() {
  // 定义窗口配置对象，包含窗口的各种属性和行为设置
  const config = {
    width: 1400,         // 窗口初始宽度（像素）
    height: 900,         // 窗口初始高度（像素）
    // alwaysOnTop: true,  // 注释掉的配置：窗口是否始终置顶
    // fullscreen: true,   // 注释掉的配置：是否全屏显示
    // transparent: true,  // 注释掉的配置：窗口是否透明
    // frame: false,       // 注释掉的配置：是否显示窗口边框
    minWidth: 980,       // 最小宽度，防止窗口被调整得过小影响用户体验
    minHeight: 780,      // 最小高度，确保UI元素有足够的显示空间
    // 让桌面应用没有边框，这样菜单栏也会消失
    show: false,         // 初始不显示窗口，等内容加载完成后再显示，避免白屏
    icon: path.join(process.cwd(), "/resources/icons/icon.ico"), // 设置应用图标路径
    // 窗口是否有阴影
    hasShadow: false,    // 禁用窗口阴影效果
    title: "FSSE教育质量监测评估", // 设置窗口标题
    webPreferences: {    // 网页功能相关设置
      nodeIntegration: true,     // 启用Node.js集成，允许渲染进程使用Node.js API
      webSecurity: false,        // 禁用同源策略等Web安全特性，允许跨域请求
      allowRunningInsecureContent: true, // 允许https页面运行http资源
      contextIsolation: false,   // 禁用上下文隔离，允许渲染进程访问Node.js环境
      webviewTag: true,          // 启用webview标签，支持嵌入其他网页
      spellcheck: false,         // 禁用拼写检查功能
      disableHtmlFullscreenWindowResize: true, // 禁用HTML全屏时的窗口大小调整
    },
  };
  // 在生产环境中应用特殊配置
  // 这些设置确保在正式环境中应用以全屏模式运行，并始终保持在前台
  if (process.env.NODE_ENV != "development") {
    config.alwaysOnTop = true;  // 窗口始终置顶，防止被其他应用遮挡
    config.fullscreen = true;   // 全屏显示，最大化可用空间
    config.frame = false;       // 无边框模式，提供更沉浸式的体验
  }

  // 使用配置创建主窗口实例
  mainWindow = new BrowserWindow(config);

  // 删除应用菜单栏，提供更简洁的界面
  // 这样用户就无法通过菜单栏访问开发者工具或执行其他操作
  mainWindow.removeMenu();

  // 注册全局快捷键，提供特殊功能访问
  createGlobalShortcut();

  // 导航完成时触发，即页面加载完成，DOM内容可用，并且onload事件已触发
  // 此时页面已完全加载，可以安全地与渲染进程通信
  mainWindow.webContents.on("did-finish-load", () => {
    // // 发送数据给渲染程序
    // mainWindow.webContents.send('something', '主进程发送到渲染进程的数据')

    // 获取设备MAC地址并发送给渲染进程
    // 这可用于设备识别、授权验证等功能
    const mac = getMac();
    mainWindow.webContents.send("currentMac", mac);
  });
  /**
   * 检查应用更新的函数
   *
   * 调用更新模块检查新版本，并根据检查结果向渲染进程发送不同类型的通知
   */
  const UpdaterCheck = () => {
    Updater.check((res) => {
      // 如果定时器存在，先清除它，避免多个定时器同时运行
      timer && clearInterval(timer);

      // 根据更新检查的结果类型，向渲染进程发送不同的消息
      if (res.type == "updateAvailable") {
        // 有可用更新：发送类型1消息，通知渲染进程有新版本可用
        mainWindow.webContents.send("updateAppAuto", { type: "1", data: res });
      } else if (res.type == "updateDownloadedEnd") {
        // 更新已下载完成：发送类型0消息，通知渲染进程可以安装更新
        mainWindow.webContents.send("updateAppAuto", { type: "0", data: res });
      } else if (res.type == "updateProgress") {
        // 更新下载进行中：发送类型2消息，通知渲染进程显示下载进度
        mainWindow.webContents.send("updateAppAuto", { type: "2", data: res });
      }
    });
  };

  // 监听渲染进程发来的更新检查请求
  ipcMain.on("UpdaterCheck", (_, data) => {
    // 仅在生产环境中执行更新检查
    if (data == "prod") {
      // 向渲染进程发送日志消息
      mainWindow.webContents.send("console", "生产环境,检测更新");

      // 立即执行一次更新检查
      UpdaterCheck();

      // 设置定时器，每10分钟检查一次更新
      // 这确保应用能及时获取最新版本，同时不会过于频繁地请求服务器
      timer = setInterval(() => {
        UpdaterCheck();
      }, 1000 * 60 * 10); // 10分钟 = 600,000毫秒
    }
  });
  // 根据启动参数决定加载模式（开发或生产）
  if (process.argv[2]) {
    // 开发模式：加载本地开发服务器提供的内容
    // 打开开发者工具，方便调试
    mainWindow.webContents.openDevTools({ mode: "right" }); // 在右侧打开开发者工具
    mainWindow.loadURL(`http://localhost:8005`); // 加载本地开发服务器地址
  } else {
    // 生产模式：使用自定义协议加载打包后的应用
    // 注册自定义app://协议，用于加载本地资源
    CustomScheme.registerScheme();
    // 使用自定义协议加载主页面
    mainWindow.loadURL(`app://index.html`);
  }

  // 注册通用窗口事件监听
  // 这会设置窗口的各种事件处理器，如关闭、最小化等
  CommonWindowEvent.listen();

  // 当主窗口首次显示时执行
  mainWindow.once("show", () => {
    // 隐藏并关闭加载窗口
    // 可选链操作符(?.)确保即使loading为null或undefined也不会报错
    loading?.hide(); // 先隐藏加载窗口
    loading?.close(); // 然后关闭加载窗口释放资源
    // 显示主窗口，完成启动过程
    mainWindow.show();
  });
}

  // 权限处理（关键部分）
  session.defaultSession.setPermissionRequestHandler(
    (webContents, permission, callback) => {
      const allowedPermissions = ['camera', 'microphone', 'geolocation']
      if (allowedPermissions.includes(permission)) {
        callback(true) // 允许权限
      } else {
        callback(false) // 拒绝其他权限
      }
    }
  )

/**
 * 注册全局快捷键
 *
 * 该函数设置应用程序的全局快捷键，这些快捷键在应用程序运行时全局有效，
 * 即使应用程序不在焦点状态也能响应这些快捷键。
 */
function createGlobalShortcut() {
  // 注册Ctrl+O或Command+O快捷键，用于打开开发者工具
  // 这提供了一种在生产环境中访问开发者工具的隐藏方式，方便调试
  globalShortcut.register("CmdOrCtrl+O", function () {
    mainWindow.webContents.openDevTools();
  });

  // 注册Ctrl+I或Command+I快捷键，用于销毁主窗口
  // 这提供了一种快速退出应用的方法，比正常关闭更直接
  globalShortcut.register("CmdOrCtrl+I", function () {
    mainWindow.destroy();
  });

  // 注册Ctrl+P或Command+P快捷键，用于清除本地存储
  // 这可用于快速重置应用状态，清除缓存数据
  globalShortcut.register("CmdOrCtrl+P", () => {
    // 向渲染进程发送清除本地存储的消息
    mainWindow.webContents.send("removeLocalStroage");
  });
}

/**
 * 创建并显示应用程序启动时的加载窗口
 *
 * @param {Function} cb - 加载窗口显示后要执行的回调函数，通常是创建主窗口的函数
 *
 * 该函数负责：
 * 1. 创建一个无边框、透明的加载窗口，用于显示应用启动过程中的加载动画
 * 2. 根据运行环境（开发/生产）加载不同路径的loading.html文件
 * 3. 设置窗口显示后的回调，延迟执行主窗口创建
 * 4. 显示加载窗口
 */
function showLoading(cb) {
  // 创建加载窗口实例，配置为无边框、透明背景的窗口
  loading = new BrowserWindow({
    show: false,        // 初始不显示，等待内容加载完成后再显示
    frame: false,       // 无窗口边框，创建无边界的视觉体验
    width: 1024,        // 窗口宽度
    height: 600,        // 窗口高度
    resizable: false,   // 禁止用户调整窗口大小
    alwaysOnTop: true,  // 窗口始终保持在最前面
    skipTaskbar: true,  // 不在任务栏显示窗口图标
    transparent: true,  // 窗口背景透明
  });

  // 根据运行环境加载不同路径的loading.html文件
  if (process.argv[2]) {
    // 开发环境：使用process.cwd()获取当前工作目录的绝对路径
    loading.loadFile(path.join(process.cwd(), "loading.html"));
  } else {
    // 生产环境：使用相对路径加载loading.html
    loading.loadFile(`./loading.html`);
  }

  // 监听窗口显示事件，窗口显示后延迟执行回调函数
  loading.once("show", () => {
    // 延迟1秒执行回调函数，通常是创建主窗口的函数
    // 这个延迟可以确保加载动画有足够时间显示，提升用户体验
    setTimeout(cb, 1000);
  });

  // 显示加载窗口
  loading.show();
}


/**
 * 应用程序就绪事件处理
 *
 * 当Electron应用程序完成初始化并准备创建浏览器窗口时，
 * 会触发这个事件，这是应用程序启动流程的关键点。
 */
app.on("ready", () => {
  /**
   * 显示加载窗口并在加载窗口显示后创建主窗口
   *
   * 这行代码是应用启动流程的核心，实现了两阶段启动策略：
   * 1. 首先调用showLoading函数创建并显示一个加载窗口，向用户提供视觉反馈
   * 2. 将createWindow函数作为回调传入，在加载窗口显示后延时执行
   * 3. 当主窗口创建并准备就绪后，加载窗口会自动关闭
   *
   * 这种方式有以下优点：
   * - 提供平滑的用户体验，避免应用启动时的白屏或冻结感
   * - 在主窗口加载过程中（可能耗时较长）保持用户参与感
   * - 分离关注点，使代码结构更清晰，易于维护
   */
  showLoading(createWindow);
});

/**
 * 获取设备MAC地址
 *
 * 该函数遍历系统网络接口，查找并返回第一个有效的MAC地址。
 * MAC地址可用于设备识别、授权验证等功能。
 *
 * @returns {string} 设备的MAC地址，如果未找到则返回空字符串
 */
function getMac() {
  //获取mac地址
  let mac = ""; // 初始化MAC地址变量为空字符串

  // 获取系统所有网络接口信息
  let networkInterfaces = os.networkInterfaces();

  // 遍历所有网络接口
  for (let i in networkInterfaces) {
    // 遍历每个接口的所有连接
    for (let j in networkInterfaces[i]) {
      // 筛选条件：
      // 1. 必须是IPv4地址族
      // 2. MAC地址不能是全0（无效地址）
      // 3. IP地址不能是本地回环地址(127.0.0.1)
      if (
        networkInterfaces[i][j]["family"] === "IPv4" &&
        networkInterfaces[i][j]["mac"] !== "00:00:00:00:00:00" &&
        networkInterfaces[i][j]["address"] !== "127.0.0.1"
      ) {
        // 找到符合条件的第一个MAC地址
        mac = networkInterfaces[i][j]["mac"];
      }
    }
  }

  // 返回找到的MAC地址，如果没找到则返回空字符串
  return mac;
}

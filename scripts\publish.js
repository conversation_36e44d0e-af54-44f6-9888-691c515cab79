/*
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2023-02-18 10:03:50
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-02-20 09:18:37
 */
import chalk from 'chalk'
import { version } from '../package.json'

const log = (content) => console.log(chalk.red(content) + '\n')

class Publish {
  static execCommand(scripts) {
    return new Promise(async (relove, reject) => {
      const { spawn } = require('child_process')
      const os = require('os');
      let cmdProcess
      if (os.platform() === 'darwin') {          
          cmdProcess = await spawn('sh', ['-c', scripts], {
            cwd: process.cwd(),
            stdio: 'inherit',
          })
      } else if (os.platform() === 'win32') {
          // Windows环境的处理代码
          cmdProcess = await spawn('cmd.exe', ['/s', '/c', scripts], {
            cwd: process.cwd(),
            stdio: 'inherit',
          })
      }   

      cmdProcess.on('exit', (code, signal) => {
        if (code || signal) {
          log(`执行 ${scripts} 命令进程异常退出`)
          log(`错误码：${code}`)
          console.log(signal)
          reject()
        }
      })
      cmdProcess.on('close', relove)
    })
  }
  // 验证版本
  static verifyVersion(cb) {
    cb()
    return false

    const axios = require('axios')
    const semver = require('semver')
    const url = `https://fsse-status.fsse.vip/app_exe/${process.env.NODE_ENV}/latest.yml`
    axios.get(url).then((res) => {
      if (res.status === 200) {
        const lastInfo = res.data.split('\n')
        const vArr = lastInfo[0].split(': ')
        const lastVersion = vArr[vArr.length - 1]

        // 如果当前发布版本比远程小不允许发布
        cb()
        return false
        if (semver.gt(version, lastVersion)) {
          log(
            `【警告】当前 ${process.env.NODE_ENV} 环境最新版本为:${lastVersion}低于本地版本${version}，请修改`
          )
        } else {
          log('比对版本验证通过')
          cb()
        }
      }
    })
  }

  static async upload() {
    await Publish.uploadToObs(`./release/latest.yml`)
    // await Publish.uploadToObs(`./release/fsse Setup ${version}.exe`, true)
  }
  static uploadToObs(filePath, isMultipartUpload = false) {
    return new Promise((relove, reject) => {
      const fs = require('fs-extra')
      const ObsClient = require('esdk-obs-nodejs')

      const dirstr = filePath.split('/')
      const lastKey = dirstr[dirstr.length - 1]

      const obsClient = new ObsClient({
        access_key_id: '03YZWGAOOB7IHUNP00I0',
        secret_access_key: 'nYD8a3XyswLDkCzBVZ6VC7rvAUoPBhQ7xGtVrJS1',
        server: 'https://obs.cn-south-1.myhuaweicloud.com',
      })

      const obsClientPrams = {
        Bucket: 'fsse-status',
        Key: `app_exe/${process.env.NODE_ENV}/${lastKey}`,
      }

      // 分段上传
      if (isMultipartUpload) {
        // 合并分段
        const uploadComplete = (UploadId, ETag) => {
          obsClient.completeMultipartUpload(
            {
              ...obsClientPrams,
              UploadId,
              Parts: [{ PartNumber: 1, ETag }],
            },
            (err, result) => {
              if (err) {
                reject(err)
              } else {
                log(`分段任务合并成功,上传完成。`)
                relove(result.CommonMsg.Status)
              }
            }
          )
        }

        // 上传分段
        const uploadPart = (UploadId) => {
          obsClient.uploadPart(
            {
              ...obsClientPrams,
              // 设置分段号，范围是1~10000
              PartNumber: 1,
              // 设置Upload ID
              UploadId,
              // 设置将要上传的大文件，其中localfile为待上传的本地文件路径，需要指定到具体的文件名
              SourceFile: filePath,
              // 设置分段大小
              PartSize: 5 * 1024 * 1024,
              // 设置分段的起始偏移大小
              Offset: 0,
            },
            (err, result) => {
              if (err) {
                reject(err)
              } else {
                if (result.CommonMsg.Status < 300 && result.InterfaceResult) {
                  uploadComplete(UploadId, result.InterfaceResult.ETag)
                }
              }
            }
          )
        }
        // 创建上传段任务
        obsClient.initiateMultipartUpload(
          {
            ...obsClientPrams,
            ContentType: 'text/plain',
          },
          (err, result) => {
            if (err) {
              reject(err)
            } else {
              if (result.CommonMsg.Status < 300 && result.InterfaceResult) {
                log(`初始化分段上传任务：${result.InterfaceResult.UploadId}`)
                uploadPart(result.InterfaceResult.UploadId)
              }
            }
          }
        )
      } else {
        const stream = fs.createReadStream(filePath)
        obsClient.putObject(
          {
            ...obsClientPrams,
            Body: stream,
          },
          (err, result) => {
            if (err) {
              reject(err)
            } else {
              relove('success')
            }
          }
        )
      }
    })
  }

  static init() {
    Publish.verifyVersion(async () => {
      const startTime = new Date().getTime()
      let endTime = 0
      log(
        `开始发布 ${process.env.NODE_ENV} 应用程序安装包，当前版本${version}。`
      )
      log(`开始更新版本...`)
      // await Publish.execCommand('standard-version')
      log(`版本已更新：${version}，开始打包...`)
      await Publish.execCommand(`vite build --mode ${process.env.NODE_ENV}`)
      log('打包完成。')
      // await Publish.upload()
      // endTime = new Date().getTime()
      // const time = ((endTime - startTime) / (1000 * 60)).toFixed(1)
      // log(`发布至 ${process.env.NODE_ENV} v${version} 完成，耗时${time}分钟。`)
    })
  }
}

Publish.init()

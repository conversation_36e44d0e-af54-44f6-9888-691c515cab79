
<template>
  <div class="version">{{ version }}</div>
  <iframe allow="microphone" :src="src" frameborder="0" class="my_iframe" id="_iframe" v-if="lineStatus"></iframe>
  <div v-else class="netWork_box">
    <img src="./pic-wlydk.png" />
    网络已断开
  </div>
  <a-spin v-if="lineStatus" class="my-spin" :tip="state.spinningText" :spinning="state.spinning"></a-spin>

</template>

<script setup>
import { onMounted, nextTick, ref ,reactive} from 'vue'
import { ipcRenderer } from 'electron'
import path from 'path'
import { createDialog } from '../../../common/dialog'
import { version } from '../../../../../package.json'
const src = import.meta.env.VITE_BASE_FSSE + `?v=${Math.random()}`
const VITE_BASE_ENV = import.meta.env.VITE_BASE_ENV


let dialog = null
const lineStatus = ref(true)

const state = reactive({
  spinning: false,
  spinningText: '加载中...',
})

async function updateApp(data) {
  let config = {
    modal: true,
    width: 500,
    height: 350,
    resizable: false,
    movable: true,
    hasShadow: false,
    title: '升级',
    icon: path.join(process.cwd(), '/resources/icons/icon.ico'),
    webPreferences: { webviewTag: false },
  }

  if (dialog) {
    dialog.postMessage(data || {})
    return false
  }
  dialog = await createDialog(`/windowSetting/update`, config)
  dialog.postMessage(data || {})
  window.addEventListener('message', e => {
    console.log(`来自子窗口的消息`, e.data)
  })
}
window.addEventListener(
  'message',
  function (e) {
    if (e.data == 'exit') {
      ipcRenderer.invoke('exit')
    }
  },
  false
)
function sendDataToIframe(data) {
  try {
    const _iframe = document.getElementById('_iframe')    

    if (_iframe?.contentWindow) {
      const _data = JSON.stringify(data)
      _iframe.contentWindow?.postMessage(_data, '*')
    }  
  } catch (error) {
    console.log(error)
  }
}

// 清楚缓存
function handleRemoveLocalStroage() {
  console.log(`remove`)
  sendDataToIframe({
    eventName: `removeLocalStroage`,
    data: '',
  })
}

// 检查当前网络状态
function updateOnlineStatus() {
  lineStatus.value = navigator.onLine
  if (navigator.onLine) {
    // 这里可以添加在线时的处理逻辑
    lineStatus.value = true
  } else {
    // 这里可以添加离线时的处理逻辑
    lineStatus.value = false
  }
}

// 检测网络状态
window.addEventListener('offline', updateOnlineStatus)
window.addEventListener('online', updateOnlineStatus)
// setInterval(updateOnlineStatus, 1000)

onMounted(() => {
  ipcRenderer.on('updateApp', (e, data) => {
    updateApp(data)
  })
  ipcRenderer.send('UpdaterCheck', VITE_BASE_ENV)
  ipcRenderer.on('updateAppAuto', (e, data) => {
    window.onbeforeunload = null
    sendDataToIframe({
      eventName: `updateApp`,
      data: data,
    })
  })


  ipcRenderer.on('removeLocalStroage', handleRemoveLocalStroage)

  // 将数据通过postmessage发送给网页iframe
  // sendDataToIframe({
  //     eventName: `currentMac`,
  //     data: window.sessionStorage.getItem('currentMac')
  // })

  function showApp(data) {
    ipcRenderer.invoke('showWindow')
  }

  window.addEventListener(
    'message',
    event => {
      console.log('origin', event.origin)
      try {
        const handle = JSON.parse(event.data)
        if (handle && handle.eventName) {
          const func = {
            showApp,
          }
          func[handle.eventName](handle.data)
        }
      } catch (error) {
        console.log(error)
      }
    },
    false
  )


  state.spinning = true
  const iframe = document.getElementById('_iframe')
  if(iframe){
    iframe.onload = function () {
      console.log(`iframe加载完成`) 
      state.spinning = false


      try {
        // 尝试访问 iframe 内容，跨域时会抛出异常
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        if(iframeDoc.body.innerHTML.length==0){
          state.spinning = true
          state.spinningText = `内容加载失败，请检查网络!`
        }
      } catch (e) {
        console.log('iframe 无法访问内容');
      }
    }

    iframe.onerror = function () {
      console.log(`iframe加载失败`)
    }
  }
})
</script>

<style scoped lang="less">
.my_iframe {
  width: 100%;
  height: 100%;
}
.version {
  position: absolute;
  bottom: 10px;
  left: 10px;
  color: #fff;
}
.netWork_box {
  color: #595959;
  font-size: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  justify-content: center;
  transform: translateY(-50px);
}

.my-spin{
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%,-50%);
}
</style>

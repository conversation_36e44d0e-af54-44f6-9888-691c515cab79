import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  { path: '/', redirect: '/windowMain/homepage' },
  {
    path: '/windowMain',
    component: () => import('../window/main/index.vue'),
    children: [
      {
        path: '/windowMain/homepage',
        component: () => import('../window/main/homepage/index.vue'),
      },
    ],
  },
  {
    path: '/windowSetting',
    component: () => import('../window/setting/index.vue'),
    children: [
      {
        path: '/windowSetting/update',
        component: () => import('../window/setting/update.vue'),
      },
    ],
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router

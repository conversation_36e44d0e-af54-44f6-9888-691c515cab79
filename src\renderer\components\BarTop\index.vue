<template>
    <div class="topBar">
        <div class="winTitle">
            <img src="https://file.1d1j.cn/fsse_logo.png" style="height:30px" alt="" />
            <span style="padding-left:4px"> {{ title }}</span>
        </div>
        <div class="winTool">
            <div @click="minimizeMainWindow" title="最小化">
                <i class="icon icon-minimize" />
            </div>
            <div v-if="isMaximized" @click="unmaximizeMainWindow" title="向下还原">
                <i class="icon icon-restore" />
            </div>
            <div v-else @click="maxmizeMainWin" title="最大化">
                <i class="icon icon-maximize" />
            </div>
            <div @click="closeWindow" title="关闭">
                <i class="icon icon-close" />
            </div>
        </div>
    </div>
</template>


<script setup>
import { onMounted, ref, onUnmounted } from 'vue'
import { ipcRenderer } from 'electron'


const props = defineProps({
    title: String
})

let isMaximized = ref(false)
let closeWindow = () => {
    ipcRenderer.invoke('closeWindow')
}
let maxmizeMainWin = () => {
    ipcRenderer.invoke('maxmizeWindow')
}
let minimizeMainWindow = () => {
    ipcRenderer.invoke('minimizeWindow')
}
let unmaximizeMainWindow = () => {
    ipcRenderer.invoke('unmaximizeWindow')
}
let winMaximizeEvent = () => {
    isMaximized.value = true
}
let winUnmaximizeEvent = () => {
    isMaximized.value = false
}
onMounted(() => {
    ipcRenderer.on('windowMaximized', winMaximizeEvent)
    ipcRenderer.on('windowUnmaximized', winUnmaximizeEvent)
})
onUnmounted(() => {
    ipcRenderer.off('windowMaximized', winMaximizeEvent)
    ipcRenderer.off('windowUnmaximized', winUnmaximizeEvent)
})
</script>

<style scoped lang="less">
    .topBar {
        display: flex;
        height: 40px;
        line-height: 40px;
        -webkit-app-region: drag;
        width: 100%;
        border-bottom: 1px solid #e6e6e673;
        background: #00a887;
    }
    .winTitle {
        flex: 1;
        padding-left: 12px;
        font-size: 14px;
        color: #fff;
        display: flex;
        align-items: center;
    }
    .winTool {
        height: 100%;
        display: flex;
        -webkit-app-region: no-drag;
    }
    .winTool div {
        height: 100%;
        width: 34px;
        text-align: center;
        color: #999;
        cursor: pointer;
        line-height: 40px;
    }
    .winTool .icon {
        font-size: 10px;
        color: #fff;
        font-weight: bold;
    }
    .winTool div:hover {
        background: rgba(0, 0, 0, 0.07);
    }
    .winTool div:last-child:hover {
        background: #ff7875;
    }
    .winTool div:last-child:hover i {
        color: #fff !important;
    }
</style>
